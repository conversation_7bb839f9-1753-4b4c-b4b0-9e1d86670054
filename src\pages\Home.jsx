// src/pages/Home.jsx
// The main landing page of the application.

import React from 'react';
import { motion } from 'framer-motion';
import Terminal from '../components/Terminal'; // Placeholder
import WeatherWidget from '../components/WeatherWidget'; // Placeholder
import ExperimentCard from '../components/ExperimentCard';

const Home = () => {
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.7,
  };

  const featuredExperiment = {
    title: "Interactive Project Showcase UI",
    description: "A reusable card component to display projects, skills, or experiments. Dynamically styled with TailwindCSS and animated with Framer Motion for engaging user interaction.",
    technologies: ["React", "TailwindCSS", "Framer Motion", "JavaScript"],
    link: "/projects" // Link to the projects page or a specific project URL
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
      // className="container mx-auto px-4 py-8 text-white" // Container classes moved to App.jsx main
    >
      {/* Welcome Section */}
      <section className="text-center mb-12">
        <motion.h1
          className="text-5xl font-bold mb-4"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, type: 'spring', stiffness: 120 }}
        >
          Welcome to Dev10x Playground!
        </motion.h1>
        <motion.p
          className="text-lg text-gray-300"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          This is a space to explore creative web experiments, UI components, and fun coding projects.
        </motion.p>
      </section>

      {/* Widgets Section - Placeholder for Terminal and Weather */}
      <section className="mb-12">
        <div className="text-center mb-6">
          <h2 className="text-3xl font-semibold inline-block">Tools & Utilities</h2>
          {/* Hint for Terminal directly under the section title or specifically near terminal */}
        </div>
        <div className="grid md:grid-cols-2 gap-8">
          <motion.div initial={{ opacity: 0, x: -50 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.6, type: 'spring' }}>
            <h3 className="text-xl font-semibold mb-2 text-center">Interactive Terminal</h3>
            <p className="text-sm text-gray-400 text-center mb-2">(Hint: Type 'help' to see available commands)</p>
            <div className="bg-slate-800 p-6 rounded-lg shadow-xl h-64 flex items-center justify-center">
              <Terminal /> {/* Actual Terminal component will render its content */}
            </div>
          </motion.div>
          <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ delay: 0.6, type: 'spring' }}>
            <h3 className="text-xl font-semibold mb-2 text-center">Mood Widget</h3>
            <p className="text-sm text-gray-400 text-center mb-2">(Click 'Change Mood' to cycle)</p>
            <div className="bg-slate-800 p-6 rounded-lg shadow-xl h-64 flex items-center justify-center">
              <WeatherWidget /> {/* Actual WeatherWidget component will render its content */}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Featured Experiment Section */}
      <section className="mb-12">
        <h2 className="text-3xl font-semibold mb-6 text-center">Featured Experiment</h2>
        <motion.div
          className="max-w-2xl mx-auto" // This container might be useful here for the card specifically
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, type: 'spring', stiffness: 100 }}
        >
          <ExperimentCard
            title={featuredExperiment.title}
            description={featuredExperiment.description}
            technologies={featuredExperiment.technologies}
            link={featuredExperiment.link}
          />
        </motion.div>
      </section>

    </motion.div>
  );
};

export default Home;
