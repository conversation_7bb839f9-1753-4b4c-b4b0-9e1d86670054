// src/pages/Home.jsx
// The main landing page of the application with enhanced modern design.

import React from 'react';
import { motion } from 'framer-motion';
import Terminal from '../components/Terminal';
import ExperimentCard from '../components/ExperimentCard';
import ForceGraph from '../components/ForceGraph';
import graphData from '../../data/graphData.json';

const Home = () => {
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.7,
  };

  const featuredExperiment = {
    title: "Interactive Project Showcase UI",
    description: "A reusable card component to display projects, skills, or experiments. Dynamically styled with TailwindCSS and animated with Framer Motion for engaging user interaction.",
    technologies: ["React", "TailwindCSS", "Framer Motion", "JavaScript"],
    link: "/projects" // Link to the projects page or a specific project URL
  };



  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
      className="relative overflow-hidden bg-black min-h-screen"
    >
      {/* Subtle background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Grid overlay */}
        <div
          className="absolute inset-0 opacity-[0.05]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}
        />
      </div>

      <div className="relative z-10 px-4 py-8">
        {/* Welcome Section - Enhanced */}
        <section className="text-center mb-16">
          <div className="relative">
            {/* Decorative elements behind title */}
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ scale: 0, rotate: 45 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.3, duration: 1, type: 'spring' }}
            >
              <div className="w-40 h-40 border-2 border-sky-400/10 rounded-full" />
            </motion.div>
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ scale: 0, rotate: -45 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.5, duration: 1, type: 'spring' }}
            >
              <div className="w-56 h-56 border border-purple-400/10 rounded-full" />
            </motion.div>

            <motion.h1
              className="relative text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-white"
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 120 }}
            >
              Welcome to Dev10x
              <motion.span
                className="block text-4xl md:text-5xl lg:text-6xl text-orange-500"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.6, type: 'spring', stiffness: 100 }}
              >
                Playground!
              </motion.span>
            </motion.h1>

            <motion.div
              className="flex justify-center mb-6"
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: "auto" }}
              transition={{ delay: 0.8, duration: 1 }}
            >
              <div className="h-1 w-48 bg-orange-500 rounded-full" />
            </motion.div>

            <motion.p
              className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
            >
              This is a space to explore{' '}
              <span className="text-white font-semibold">creative web experiments</span>,{' '}
              <span className="text-gray-300 font-semibold">UI components</span>, and{' '}
              <span className="text-orange-500 font-semibold">fun coding projects</span>.
            </motion.p>
          </div>
        </section>

        {/* Professional Skills Graph Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <motion.div
              className="inline-block relative"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, type: 'spring' }}
            >
              <h2 className="text-3xl md:text-4xl font-semibold text-white">
                Minhas Competências
              </h2>
              <motion.div
                className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-orange-500 rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              />
            </motion.div>
          </div>

          <div className="max-w-5xl mx-auto">
            <motion.div
              className="relative group"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, type: 'spring' }}
            >
              <div className="relative bg-gray-900 border border-gray-700 rounded-2xl overflow-hidden">
                <div className="bg-gray-800 p-4 border-b border-gray-700">
                  <h3 className="text-xl font-semibold text-center text-white">Mapa de Habilidades</h3>
                  <p className="text-sm text-gray-400 text-center mt-1">
                    Visualização interativa das minhas áreas de atuação profissional
                  </p>
                </div>
                <div className="p-6 flex justify-center">
                  <ForceGraph data={graphData} width={600} height={400} compact={true} />
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Terminal Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <motion.div
              className="inline-block relative"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, type: 'spring' }}
            >
              <h2 className="text-3xl md:text-4xl font-semibold text-white">
                Interactive Terminal
              </h2>
              <motion.div
                className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-orange-500 rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 0.8, duration: 0.6 }}
              />
            </motion.div>
          </div>

          <div className="max-w-4xl mx-auto">
            <motion.div
              className="relative group"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, type: 'spring' }}
            >
              <div className="relative bg-gray-900 border border-gray-700 rounded-2xl overflow-hidden">
                <div className="bg-gray-800 p-4 border-b border-gray-700">
                  <h3 className="text-xl font-semibold text-center text-white">Terminal</h3>
                  <p className="text-sm text-gray-400 text-center mt-1">
                    Type '<span className="text-orange-500 font-mono">help</span>' to see available commands
                  </p>
                </div>
                <div className="p-6">
                  <Terminal />
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Featured Experiment Section - Enhanced */}
        <section className="mb-12">
          <div className="text-center mb-12">
            <motion.div
              className="inline-block relative"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.8, type: 'spring' }}
            >
              <h2 className="text-3xl md:text-4xl font-semibold text-white">
                Featured Experiment
              </h2>
              <motion.div
                className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-orange-500 rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ delay: 1, duration: 0.6 }}
              />
            </motion.div>
          </div>

          <motion.div
            className="max-w-3xl mx-auto relative group"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8, type: 'spring', stiffness: 100 }}
          >
            {/* Subtle glow effect */}
            <div className="absolute -inset-2 bg-gray-800/20 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700" />

            {/* Floating badge */}
            <motion.div
              className="absolute -top-4 -right-4 z-10 bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg"
              initial={{ scale: 0, rotate: -45 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 1.2, type: 'spring', stiffness: 200 }}
              whileHover={{
                scale: 1.1,
                rotate: 5,
                transition: { duration: 0.2 }
              }}
            >
              ⭐ Featured
            </motion.div>

            <div className="relative">
              <ExperimentCard
                title={featuredExperiment.title}
                description={featuredExperiment.description}
                technologies={featuredExperiment.technologies}
                link={featuredExperiment.link}
              />
            </div>
          </motion.div>
        </section>

        {/* Bottom decorative elements */}
        <motion.div
          className="flex justify-center mt-16 mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.4, duration: 0.5 }}
        >
          <div className="flex space-x-4">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="w-3 h-3 bg-orange-500 rounded-full"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.4, 1, 0.4],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: "easeInOut",
                }}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Home;