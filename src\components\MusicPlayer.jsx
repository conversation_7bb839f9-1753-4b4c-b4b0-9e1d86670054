// src/components/MusicPlayer.jsx
// A simple LoFi music player component.

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlay, FaPause, FaVolumeUp, FaVolumeMute, FaVolumeDown } from 'react-icons/fa'; // Using react-icons

const MusicPlayer = () => {
  const track = {
    title: "LoFi Chill Beat",
    url: "/audio/lofi-sample.mp3" // Path from public directory
  };

  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.5); // Default volume 50%
  const [progress, setProgress] = useState(0); // Percentage
  const [duration, setDuration] = useState(0); // Total duration in seconds
  const [isMuted, setIsMuted] = useState(false);
  const [lastVolume, setLastVolume] = useState(volume); // To restore volume after unmuting

  const audioRef = useRef(null);

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch(error => console.error("Error playing audio:", error));
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
    setIsMuted(newVolume === 0);
    if (newVolume > 0 && isMuted) setIsMuted(false); // Unmute if volume is increased
  };

  const toggleMute = () => {
    if (audioRef.current) {
        if (isMuted) {
            audioRef.current.volume = lastVolume > 0 ? lastVolume : 0.1; // Restore to last volume or a small default
            setVolume(lastVolume > 0 ? lastVolume : 0.1);
            setIsMuted(false);
        } else {
            setLastVolume(volume); // Save current volume before muting
            audioRef.current.volume = 0;
            setVolume(0);
            setIsMuted(true);
        }
    }
  };


  const handleProgressChange = (e) => {
    const newProgress = parseFloat(e.target.value);
    setProgress(newProgress);
    if (audioRef.current && duration > 0) {
      audioRef.current.currentTime = (newProgress / 100) * duration;
    }
  };

  // Audio event handlers
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const setAudioData = () => {
      setDuration(audio.duration);
      setVolume(audio.volume); // Reflect actual initial volume
      setIsMuted(audio.muted);
    };

    const setAudioTime = () => {
      setProgress((audio.currentTime / audio.duration) * 100 || 0);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
        setIsPlaying(false);
        setProgress(0); // Reset progress
        // For now, just stops. Could implement play next or loop.
        // audio.currentTime = 0; // Optional: reset time to start
    };

    audio.addEventListener('loadedmetadata', setAudioData);
    audio.addEventListener('timeupdate', setAudioTime);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('volumechange', () => { // Sync with external volume changes (e.g. media keys)
        setVolume(audio.volume);
        setIsMuted(audio.muted);
    });


    // Cleanup
    return () => {
      audio.removeEventListener('loadedmetadata', setAudioData);
      audio.removeEventListener('timeupdate', setAudioTime);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('volumechange', () => {
        setVolume(audio.volume);
        setIsMuted(audio.muted);
      });
    };
  }, [duration]); // Re-attach if duration changes (e.g. new track, though not implemented here)

  const formatTime = (timeInSeconds) => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
  };

  const VolumeIcon = () => {
    if (isMuted || volume === 0) return <FaVolumeMute size={20} />;
    if (volume < 0.5) return <FaVolumeDown size={20} />;
    return <FaVolumeUp size={20} />;
  };

  return (
    <div className="p-4 bg-slate-800 rounded-lg shadow-xl text-white w-full max-w-md mx-auto">
      <audio ref={audioRef} src={track.url} preload="metadata" />

      <div className="flex items-center justify-between mb-3">
        <div className="text-sm">
            <p className="font-semibold">{track.title}</p>
            <p className="text-xs text-gray-400">Ambient LoFi Beats</p>
        </div>
        <motion.button
            onClick={togglePlayPause}
            className="p-2 rounded-full hover:bg-slate-700 transition-colors"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            aria-label={isPlaying ? "Pause" : "Play"}
          >
            <AnimatePresence mode="wait" initial={false}>
              {isPlaying ? (
                <motion.div key="pause" initial={{ opacity: 0, scale: 0.5 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.5 }}>
                  <FaPause size={24} />
                </motion.div>
              ) : (
                <motion.div key="play" initial={{ opacity: 0, scale: 0.5 }} animate={{ opacity: 1, scale: 1 }} exit={{ opacity: 0, scale: 0.5 }}>
                  <FaPlay size={24} />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.button>
      </div>

      {/* Progress Bar */}
      <div className="flex items-center space-x-2 mb-3">
        <span className="text-xs w-10 text-center">{formatTime(audioRef.current?.currentTime || 0)}</span>
        <input
          type="range"
          min="0"
          max="100"
          value={progress || 0} // Ensure value is never NaN
          onChange={handleProgressChange}
          className="flex-grow h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer accent-sky-500"
        />
        <span className="text-xs w-10 text-center">{formatTime(duration || 0)}</span>
      </div>

      {/* Volume Control */}
      <div className="flex items-center space-x-2 justify-center">
        <motion.button
            onClick={toggleMute}
            className="p-2 hover:bg-slate-700 rounded-full"
            whileHover={{scale: 1.1}}
            aria-label={isMuted ? "Unmute" : "Mute"}
        >
            <VolumeIcon />
        </motion.button>
        <input
          type="range"
          min="0"
          max="1"
          step="0.01"
          value={volume}
          onChange={handleVolumeChange}
          className="w-24 h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer accent-sky-500"
          aria-label="Volume"
        />
      </div>
    </div>
  );
};

export default MusicPlayer;
