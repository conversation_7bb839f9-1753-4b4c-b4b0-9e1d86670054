import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

const ForceGraph = ({ data, width = 800, height = 600, compact = false }) => {
  const svgRef = useRef();

  useEffect(() => {
    if (!data || !data.nodes || !data.links) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove(); // Clear previous content

    const actualWidth = compact ? Math.min(width, 600) : width;
    const actualHeight = compact ? Math.min(height, 400) : height;

    svg.attr("width", actualWidth).attr("height", actualHeight);

    // Create a copy of the data to avoid mutating the original
    const nodes = data.nodes.map(d => ({ ...d }));
    const links = data.links.map(d => ({ ...d }));

    // Define color scheme based on groups
    const getNodeColor = (group) => {
      const colors = {
        0: "#ff6b35", // Alexandre - Orange
        1: "#ffffff", // Programador - White
        2: "#d1d5db", // Designer - Light Gray
        3: "#9ca3af", // Audiovisual - Medium Gray
        4: "#6b7280", // Visão - Dark Gray
      };
      return colors[group] || "#ffffff";
    };

    // Create the simulation
    const simulation = d3.forceSimulation(nodes)
      .force("link", d3.forceLink(links).id(d => d.id).distance(compact ? 80 : 120))
      .force("charge", d3.forceManyBody().strength(compact ? -200 : -400))
      .force("center", d3.forceCenter(actualWidth / 2, actualHeight / 2));

    // Create links
    const link = svg.append("g")
      .attr("stroke", "#4b5563")
      .attr("stroke-opacity", 0.6)
      .selectAll("line")
      .data(links)
      .join("line")
      .attr("stroke-width", d => Math.sqrt(d.value) * 1.5);

    // Create nodes
    const node = svg.append("g")
      .attr("stroke", "#000")
      .attr("stroke-width", 2)
      .selectAll("circle")
      .data(nodes)
      .join("circle")
      .attr("r", d => d.size || 20)
      .attr("fill", d => getNodeColor(d.group))
      .call(d3.drag()
        .on("start", dragstarted)
        .on("drag", dragged)
        .on("end", dragended));

    // Add labels
    const label = svg.append("g")
      .selectAll("text")
      .data(nodes)
      .join("text")
      .text(d => d.id)
      .attr("font-size", d => d.group === 0 ? 14 : 11)
      .attr("font-family", "Arial, sans-serif")
      .attr("font-weight", d => d.group === 0 ? "bold" : "normal")
      .attr("fill", d => d.group === 0 ? "#000" : "#fff")
      .attr("text-anchor", "middle")
      .attr("dy", "0.35em");

    // Add title for hover
    node.append("title")
      .text(d => `${d.id}\nÁrea: ${getGroupName(d.group)}`);

    // Helper function to get group names
    function getGroupName(group) {
      const names = {
        0: "Profissional",
        1: "Desenvolvimento",
        2: "Design",
        3: "Audiovisual",
        4: "Visão Estratégica"
      };
      return names[group] || "Outros";
    }

    // Update positions on simulation tick
    simulation.on("tick", () => {
      link
        .attr("x1", d => d.source.x)
        .attr("y1", d => d.source.y)
        .attr("x2", d => d.target.x)
        .attr("y2", d => d.target.y);

      node
        .attr("cx", d => d.x)
        .attr("cy", d => d.y);

      label
        .attr("x", d => d.x)
        .attr("y", d => d.y);
    });

    // Drag functions
    function dragstarted(event, d) {
      if (!event.active) simulation.alphaTarget(0.3).restart();
      d.fx = d.x;
      d.fy = d.y;
    }

    function dragged(event, d) {
      d.fx = event.x;
      d.fy = event.y;
    }

    function dragended(event, d) {
      if (!event.active) simulation.alphaTarget(0);
      d.fx = null;
      d.fy = null;
    }

    // Cleanup function
    return () => {
      simulation.stop();
    };
  }, [data]);

  return (
    <div className="flex flex-col items-center">
      <h2 className="text-xl font-semibold mb-4 text-gray-100">Interactive Force Graph</h2>
      <div className="bg-gray-800 rounded-lg p-4 shadow-lg">
        <svg ref={svgRef} className="border border-gray-600 rounded"></svg>
      </div>
      <p className="text-sm text-gray-400 mt-2 max-w-md text-center">
        Drag nodes to interact with the force simulation. Nodes are colored by group and connected by relationships.
      </p>
    </div>
  );
};

export default ForceGraph;
