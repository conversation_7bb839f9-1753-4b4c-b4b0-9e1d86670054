import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import Terminal from './Terminal'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }) => children,
}))

const TerminalWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
)

describe('Terminal Component', () => {
  it('renders welcome message', () => {
    render(
      <TerminalWrapper>
        <Terminal />
      </TerminalWrapper>
    )
    
    expect(screen.getByText("Welcome to Dev10x Terminal. Type 'help' for available commands.")).toBeInTheDocument()
  })

  it('displays help command output', () => {
    render(
      <TerminalWrapper>
        <Terminal />
      </TerminalWrapper>
    )
    
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'help' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    expect(screen.getByText('Available commands:')).toBeInTheDocument()
    expect(screen.getByText('  help          - Show this help message.')).toBeInTheDocument()
  })

  it('displays whoami command output', () => {
    render(
      <TerminalWrapper>
        <Terminal />
      </TerminalWrapper>
    )
    
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'whoami' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    expect(screen.getByText('User: guest')).toBeInTheDocument()
    expect(screen.getByText('Developer: A curious mind exploring AI-assisted development.')).toBeInTheDocument()
  })

  it('displays date command output', () => {
    render(
      <TerminalWrapper>
        <Terminal />
      </TerminalWrapper>
    )
    
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'date' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    // Check that some date text is displayed (we can't check exact date as it changes)
    const dateElements = screen.getAllByText(/\d/)
    expect(dateElements.length).toBeGreaterThan(0)
  })

  it('handles unknown commands', () => {
    render(
      <TerminalWrapper>
        <Terminal />
      </TerminalWrapper>
    )
    
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'unknown' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    expect(screen.getByText('command not found: unknown')).toBeInTheDocument()
  })

  it('clears terminal with clear command', () => {
    render(
      <TerminalWrapper>
        <Terminal />
      </TerminalWrapper>
    )
    
    const input = screen.getByRole('textbox')
    
    // Add some commands first
    fireEvent.change(input, { target: { value: 'help' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    // Clear the terminal
    fireEvent.change(input, { target: { value: 'clear' } })
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' })
    
    // Should only have the welcome message
    const welcomeMessages = screen.getAllByText("Welcome to Dev10x Terminal. Type 'help' for available commands.")
    expect(welcomeMessages).toHaveLength(1)
  })
})
