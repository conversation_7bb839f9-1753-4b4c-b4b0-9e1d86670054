// src/App.jsx
// Main application component.
// Sets up routing and global layout (<PERSON><PERSON>, <PERSON>er).
// Imports global styles.

import React from 'react';
import './index.css'; // Import main CSS file with Tailwind
import './App.css'; // Import enhanced App styles
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Projects from './pages/Projects';
import GraphPage from './pages/Graph'; // Import GraphPage

function App() {
  return (
    <BrowserRouter>
      <div className="flex flex-col min-h-screen bg-black text-gray-100">
        <Header />
        <main className="flex-grow pt-20"> {/* Add padding-top to account for fixed header */}
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/projects" element={<Projects />} />
                <Route path="/graph" element={<GraphPage />} /> {/* Add GraphPage route */}
          </Routes>
        </main>
        <Footer />
      </div>
    </BrowserRouter>
  );
}

export default App;
