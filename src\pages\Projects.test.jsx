import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import Projects from './Projects'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }) => children,
}))

const ProjectsWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
)

describe('Projects Page', () => {
  it('renders the main heading', () => {
    render(
      <ProjectsWrapper>
        <Projects />
      </ProjectsWrapper>
    )
    
    expect(screen.getByText('My Experiments & Projects')).toBeInTheDocument()
  })

  it('renders project cards', () => {
    render(
      <ProjectsWrapper>
        <Projects />
      </ProjectsWrapper>
    )
    
    expect(screen.getByText('Interactive Terminal UI')).toBeInTheDocument()
    expect(screen.getByText('Emotional Weather Widget')).toBeInTheDocument()
    expect(screen.getByText('LoFi Music Player')).toBeInTheDocument()
  })

  it('renders the prompt playground section', () => {
    render(
      <ProjectsWrapper>
        <Projects />
      </ProjectsWrapper>
    )
    
    expect(screen.getByText('Interactive Tools')).toBeInTheDocument()
  })

  it('displays project technologies', () => {
    render(
      <ProjectsWrapper>
        <Projects />
      </ProjectsWrapper>
    )
    
    // Check for some technology tags
    expect(screen.getByText('React')).toBeInTheDocument()
    expect(screen.getByText('TailwindCSS')).toBeInTheDocument()
  })
})
