// src/components/Header.jsx
// Provides navigation links and site branding.

import React from 'react';
import { NavLink } from 'react-router-dom';
import { motion } from 'framer-motion';

const Header = () => {
  const activeClassName = "text-orange-500";
  const inactiveClassName = "text-gray-300 hover:text-white";

  const navLinkClasses = ({ isActive }) =>
    isActive ? activeClassName : inactiveClassName;

  const motionProps = {
    whileHover: { scale: 1.1 },
    transition: { type: 'spring', stiffness: 300 },
  };

  return (
    <header className="bg-black text-white p-4 fixed top-0 left-0 right-0 z-50 shadow-lg border-b border-gray-800"> {/* Fixed header */}
      <div className="container mx-auto flex flex-col md:flex-row justify-between md:items-center">
        <motion.div
          whileHover={{ scale: 1.05 }} // Slightly reduced hover scale for title
          transition={{ type: 'spring', stiffness: 300 }}
          className="self-start md:self-center mb-4 md:mb-0" // Ensure title is aligned left on mobile, centered with nav on larger
        >
          <NavLink to="/" className="text-2xl font-bold">
            Dev10x Playground
          </NavLink>
        </motion.div>
        <nav className="w-full md:w-auto"> {/* Nav takes full width on mobile for better stacking */}
          <ul className="flex flex-col md:flex-row md:space-x-4 space-y-2 md:space-y-0 items-start md:items-center"> {/* Links align left on mobile */}
            <li>
              <motion.div {...motionProps}>
                <NavLink to="/" className={navLinkClasses} end> {/* Added 'end' prop for more precise active matching on root */}
                  Home
                </NavLink>
              </motion.div>
            </li>
            <li>
              <motion.div {...motionProps}>
                <NavLink to="/about" className={navLinkClasses}>
                  About
                </NavLink>
              </motion.div>
            </li>
            <li>
              <motion.div {...motionProps}>
                <NavLink to="/projects" className={navLinkClasses}>
                  Projects
                </NavLink>
              </motion.div>
            </li>
            <li>
              <motion.div {...motionProps}>
                <NavLink to="/graph" className={navLinkClasses}>
                  Graph
                </NavLink>
              </motion.div>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  );
};

export default Header;
