/* src/index.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent; /* Disable tap highlight on mobile */
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #0f172a; /* bg-slate-900 */
  color: #f3f4f6; /* text-gray-100 */
  overflow-x: hidden; /* Prevent horizontal scroll */
}

/* Subtle Scrollbar Styling for Webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px; /* For horizontal scrollbars if they appear */
}

::-webkit-scrollbar-track {
  background: #1e293b; /* slate-800 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #475569; /* slate-600 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b; /* slate-500 */
}

/* Basic reset or defaults for some elements if needed */
button, input, select, textarea {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
  border-radius: 0; /* Remove default radius if Tailwind is to control it */
  border: none; /* Remove default borders */
  background: transparent; /* Remove default backgrounds */
}

/* Default button styling (if not using Tailwind @apply for all buttons) */
/* For this project, Tailwind classes are applied directly, so global button styles are minimal */
button {
  cursor: pointer;
}

/* Remove default list styling if Tailwind is to control it */
ul, ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Remove default anchor styling if Tailwind is to control it */
a {
  color: inherit;
  text-decoration: none;
}

/* Ensure images/SVGs are responsive by default */
img, svg, video, canvas, audio, iframe, embed, object {
  display: block;
  max-width: 100%;
}
