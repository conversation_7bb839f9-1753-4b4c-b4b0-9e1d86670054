# Dev10x Playground: Phase 2 Tasks

This document outlines the development tasks for Phase 2, focusing on implementing the core interactive features of the Dev10x Playground.

## Core Component Implementation

1.  **Header Component (`Header.jsx`):**
    -   Implement navigation links (Home, About, Projects).
    -   Style using TailwindCSS.
    -   Consider a simple animation on hover/load with Framer Motion.

2.  **Footer Component (`Footer.jsx`):**
    -   Add copyright text (e.g., "© [Year] [Your Name/Alias]").
    -   Include links to relevant profiles (GitHub, LinkedIn, etc.).
    -   Style using TailwindCSS.

3.  **Terminal Component (`Terminal.jsx`):**
    -   Develop basic terminal UI (input line, output area).
    -   Implement a few mock commands (e.g., `help`, `whoami`, `projects`, `clear`).
    -   Style to resemble a real terminal.
    -   Add subtle animations for command output using Framer Motion.

4.  **WeatherWidget Component (`WeatherWidget.jsx`):**
    -   Design a visual representation of "emotional weather" (e.g., icons, colors, text).
    -   Allow manual or simulated updates to the "weather."
    -   Style creatively using TailwindCSS.
    -   Animate transitions between states with Framer Motion.

5.  **MusicPlayer Component (`MusicPlayer.jsx`):**
    -   Integrate a simple audio player for one or a few LoFi tracks.
    -   Implement play/pause controls.
    -   (Optional) Volume control and track display.
    -   Style minimally using TailwindCSS.

6.  **ExperimentCard Component (`ExperimentCard.jsx`):**
    -   Finalize the design for how experiments are displayed.
    -   Ensure it can dynamically render content passed via props.
    -   Add hover effects or entrance animations with Framer Motion.

## Page Implementation

1.  **HomePage (`Home.jsx`):**
    -   Integrate `Terminal`, `WeatherWidget`, and potentially a featured `ExperimentCard`.
    -   Create an engaging layout.

2.  **AboutPage (`About.jsx`):**
    -   Write content about the developer's philosophy, skills, and how AI is used.
    -   Style for readability.

3.  **ProjectsPage (`Projects.jsx`):**
    -   Dynamically render multiple `ExperimentCard` components based on a data source (e.g., a simple array of project details).
    -   Implement layout for project showcase (e.g., grid).

## General Tasks

-   **Routing:** Implement client-side routing (e.g., using `react-router-dom`).
-   **State Management:** Decide on and implement state management if needed (e.g., Context API, Zustand, Redux Toolkit) for features like the MusicPlayer or Terminal state.
-   **Styling Refinements:** Ensure consistent and polished styling across the application.
-   **Responsiveness:** Ensure the layout is responsive across different screen sizes.
-   **Accessibility (A11y):** Basic checks for accessibility.
-   **Testing:** (Optional for Phase 2, but good to consider) Add basic unit tests for key components or utility functions.
-   **README Updates:** Update `README.md` with final details post-implementation.

---

# Dev10x Playground: Phase 3 Tasks

This document outlines the development tasks for Phase 3, focusing on adding new interactive elements and a data visualization component.

*Timestamp: 2024-07-30 10:00*

## New Features Implementation

1.  **Graph Page (`/pages/Graph.jsx`):**
    -   Status: Completed - *Timestamp: 2024-07-30 10:00*
    -   Import and render the `ForceGraph.jsx` component.
    -   Use mock `graphData.json` from `/data` folder.
    -   Add subtle Framer Motion animations when graph loads.
    -   Add a visible route `/graph`, accessible via header nav.

2.  **Prompt Playground (on Projects Page):**
    -   Status: Completed - *Timestamp: 2024-07-30 10:00*
    -   Create a component `PromptPlayground.jsx` displaying prewritten AI prompts from `prompts.js`.
    -   Include animations and a simple way to copy prompts with 1 click.

3.  **Terminal Easter Egg (`sudo dance`):**
    -   Status: Completed - *Timestamp: 2024-07-30 10:00*
    -   Implement `sudo dance` command in `Terminal.jsx`.
    -   Trigger a Lofi-mode animation (glow effect).
