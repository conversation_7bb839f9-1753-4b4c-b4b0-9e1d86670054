// src/components/WeatherWidget.jsx
// A widget to display a fictional "emotional weather" status.

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const emotionalWeathers = [
  {
    id: 'creative',
    emotion: 'Creative Flow',
    icon: '💡',
    color: 'bg-yellow-500', // Brighter yellow
    textColor: 'text-gray-900', // Darker text for contrast
    description: "Ideas are sparking! Great time for brainstorming and starting new projects."
  },
  {
    id: 'focused',
    emotion: 'Deep Focus',
    icon: '🎯',
    color: 'bg-sky-600',
    textColor: 'text-white',
    description: "Zeroed in and making progress. Minimal distractions, maximum output."
  },
  {
    id: 'productive',
    emotion: 'Productive Pulse',
    icon: '🚀',
    color: 'bg-green-500',
    textColor: 'text-white',
    description: "Ticking off tasks and achieving goals. Momentum is high!"
  },
  {
    id: 'relaxed',
    emotion: 'Relaxed & Recharging',
    icon: '🧘',
    color: 'bg-teal-500',
    textColor: 'text-white',
    description: "Calm and collected. Perfect for unwinding or light, thoughtful work."
  },
  {
    id: 'contemplative',
    emotion: 'Contemplative Mood',
    icon: '🤔',
    color: 'bg-indigo-500',
    textColor: 'text-white',
    description: "Reflecting and strategizing. Good for planning and problem-solving."
  },
  {
    id: 'learning',
    emotion: 'Learning Mode',
    icon: '📚',
    color: 'bg-purple-500',
    textColor: 'text-white',
    description: "Absorbing new information and expanding skillsets. Curiosity is piqued!"
  }
];

const WeatherWidget = () => {
  const [weatherIndex, setWeatherIndex] = useState(0);
  const currentWeather = emotionalWeathers[weatherIndex];

  const changeWeather = () => {
    setWeatherIndex((prevIndex) => (prevIndex + 1) % emotionalWeathers.length);
  };

  const contentAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { type: 'spring', stiffness: 300, damping: 25, duration: 0.3 }
  };

  return (
    <div
      className={`p-6 rounded-lg shadow-xl h-full flex flex-col items-center justify-between text-center transition-colors duration-500 ease-in-out ${currentWeather.color} ${currentWeather.textColor}`}
    >
      <AnimatePresence mode="wait"> {/* mode="wait" ensures exit animation completes before enter */}
        <motion.div
          key={currentWeather.id} // Key change triggers AnimatePresence
          variants={contentAnimation}
          initial="initial"
          animate="animate"
          exit="exit"
          className="flex flex-col items-center justify-center flex-grow"
        >
          <div className="text-6xl mb-4">{currentWeather.icon}</div>
          <h3 className="text-3xl font-semibold mb-2">{currentWeather.emotion}</h3>
          <p className="text-sm leading-relaxed">{currentWeather.description}</p>
        </motion.div>
      </AnimatePresence>

      <motion.button
        onClick={changeWeather}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`mt-6 py-2 px-6 rounded-lg font-semibold shadow-md focus:outline-none focus:ring-2 focus:ring-opacity-75
                    ${currentWeather.textColor === 'text-white' ? 'bg-white/20 hover:bg-white/30 text-white ring-white'
                                                               : 'bg-black/20 hover:bg-black/30 text-gray-900 ring-gray-900'}`}
      >
        Change Mood
      </motion.button>
    </div>
  );
};

export default WeatherWidget;
