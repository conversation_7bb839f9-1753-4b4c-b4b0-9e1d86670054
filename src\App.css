/* Enhanced App.css with modern design elements */

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow-x: hidden;
}

/* Background effects */
body {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  min-height: 100vh;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(14, 165, 233, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%);
  z-index: -1;
  animation: gradientShift 15s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Enhanced logo styles */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter, transform;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa) drop-shadow(0 0 4em #646cffaa);
  transform: scale(1.05) rotate(5deg);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa) drop-shadow(0 0 4em #61dafbaa);
  transform: scale(1.05) rotate(-5deg);
}

/* Enhanced logo spin animation */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear, float 4s ease-in-out infinite;
  }
}

/* Enhanced card styles */
.card {
  padding: 2em;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.card:hover::before {
  left: 100%;
}

.card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(14, 165, 233, 0.3);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(14, 165, 233, 0.1);
}

/* Enhanced text styles */
.read-the-docs {
  color: #94a3b8;
  transition: color 0.3s ease;
}

.read-the-docs:hover {
  color: #e2e8f0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #0ea5e9, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #38bdf8, #a78bfa);
}

/* Glow effects for interactive elements */
.glow-on-hover {
  position: relative;
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 
    0 0 20px rgba(14, 165, 233, 0.4),
    0 0 40px rgba(14, 165, 233, 0.2),
    0 0 60px rgba(14, 165, 233, 0.1);
}

/* Animated background particles effect */
.particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(14, 165, 233, 0.5);
  border-radius: 50%;
  animation: float-particles 20s infinite linear;
}

@keyframes float-particles {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(100px);
    opacity: 0;
  }
}

/* Enhanced focus states for accessibility */
*:focus {
  outline: 2px solid rgba(14, 165, 233, 0.6);
  outline-offset: 2px;
}

/* Custom selection styling */
::selection {
  background: rgba(14, 165, 233, 0.3);
  color: white;
}

/* Responsive improvements */
@media (max-width: 768px) {
  #root {
    padding: 1rem;
  }
  
  .card {
    padding: 1.5em;
  }
  
  .logo {
    height: 4em;
    padding: 1em;
  }
}