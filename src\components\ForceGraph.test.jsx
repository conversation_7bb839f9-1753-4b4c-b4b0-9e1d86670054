import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import ForceGraph from './ForceGraph'

// Mock D3 to avoid complex DOM manipulation in tests
vi.mock('d3', () => {
  const mockChain = {
    attr: vi.fn(() => mockChain),
    selectAll: vi.fn(() => mockChain),
    data: vi.fn(() => mockChain),
    join: vi.fn(() => mockChain),
    call: vi.fn(() => mockChain),
    append: vi.fn(() => mockChain),
    text: vi.fn(() => mockChain),
    remove: vi.fn(() => mockChain),
    on: vi.fn(() => mockChain),
    force: vi.fn(() => mockChain),
    id: vi.fn(() => mockChain),
    distance: vi.fn(() => mockChain),
    strength: vi.fn(() => mockChain),
    alphaTarget: vi.fn(() => mockChain),
    restart: vi.fn(() => mockChain),
    stop: vi.fn(() => mockChain)
  }

  return {
    select: vi.fn(() => mockChain),
    forceSimulation: vi.fn(() => mockChain),
    forceLink: vi.fn(() => mockChain),
    forceManyBody: vi.fn(() => mockChain),
    forceCenter: vi.fn(() => mockChain),
    drag: vi.fn(() => mockChain),
    schemeCategory10: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
  }
})

describe('ForceGraph Component', () => {
  const mockData = {
    nodes: [
      { id: 'A', group: 1 },
      { id: 'B', group: 2 }
    ],
    links: [
      { source: 'A', target: 'B', value: 1 }
    ]
  }

  it('renders the force graph title', () => {
    render(<ForceGraph data={mockData} />)
    expect(screen.getByText('Interactive Force Graph')).toBeInTheDocument()
  })

  it('renders the description text', () => {
    render(<ForceGraph data={mockData} />)
    expect(screen.getByText(/Drag nodes to interact with the force simulation/)).toBeInTheDocument()
  })

  it('renders an SVG element', () => {
    render(<ForceGraph data={mockData} />)
    const svg = document.querySelector('svg')
    expect(svg).toBeInTheDocument()
  })

  it('handles empty data gracefully', () => {
    render(<ForceGraph data={null} />)
    expect(screen.getByText('Interactive Force Graph')).toBeInTheDocument()
  })

  it('handles missing nodes or links', () => {
    render(<ForceGraph data={{ nodes: [] }} />)
    expect(screen.getByText('Interactive Force Graph')).toBeInTheDocument()
  })
})
