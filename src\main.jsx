// src/main.jsx
// Entry point of the React application.
// Renders the root App component.
// Framer Motion context provider could be set up here if needed globally.

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.jsx';
// import { MotionConfig } from 'framer-motion'; // Example import

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    {/* <MotionConfig reducedMotion="user"> */}
      <App />
    {/* </MotionConfig> */}
  </React.StrictMode>,
);
