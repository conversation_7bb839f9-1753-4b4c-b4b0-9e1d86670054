// src/components/ExperimentCard.jsx
// Displays information about a single experiment or project.

import React from 'react';
import { motion } from 'framer-motion';

const ExperimentCard = ({ title, description, technologies, link }) => {
  const cardVariants = {
    initial: { scale: 1, y: 0 },
    hover: {
      scale: 1.03,
      y: -5,
      boxShadow: "0px 10px 20px rgba(0,0,0,0.2)", // Enhanced shadow on hover
    }
  };

  const transitionProps = {
    type: 'spring',
    stiffness: 200,
    damping: 15
  };

  const renderContent = () => (
    <>
      <h3 className="text-xl font-bold text-white mb-2">{title}</h3>
      <p className="text-gray-300 text-sm mb-3">{description}</p>
      <div className="mb-4">
        <h4 className="text-sm font-semibold text-gray-200 mb-1">Technologies:</h4>
        <div className="flex flex-wrap">
          {technologies.map((tech, index) => (
            <span
              key={index}
              className="bg-sky-600 text-white text-xs px-2 py-1 rounded-full mr-1 mb-1"
            >
              {tech}
            </span>
          ))}
        </div>
      </div>
      {link && (
        <div className="mt-auto pt-2">
          {/* Pushes button to the bottom if card content is short */}
          <a
            href={link}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-block bg-yellow-500 text-gray-900 hover:bg-yellow-400 font-semibold py-2 px-4 rounded-lg transition-colors duration-300 text-sm"
          >
            View Project
          </a>
        </div>
      )}
    </>
  );

  return (
    <motion.div
      className="bg-slate-800 rounded-lg p-6 shadow-lg flex flex-col h-full" // Added h-full for consistent card height if in a grid
      variants={cardVariants}
      initial="initial"
      whileHover="hover"
      transition={transitionProps}
    >
      {renderContent()}
    </motion.div>
  );
};

export default ExperimentCard;
