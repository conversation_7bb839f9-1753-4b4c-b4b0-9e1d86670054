// src/pages/Projects.jsx
// Showcases a collection of projects or experiments.

import React from 'react';
import { motion } from 'framer-motion';
import ExperimentCard from '../components/ExperimentCard';
import PromptPlayground from '../components/PromptPlayground'; // Import PromptPlayground

const projectsData = [
  {
    id: 1,
    title: "Interactive Terminal UI",
    description: "A mock terminal interface that simulates basic command execution and directory navigation. Built with React and styled with TailwindCSS for a retro-modern feel.",
    technologies: ["React", "TailwindCSS", "JavaScript", "State Management"],
    link: "#terminal-demo" // Placeholder link
  },
  {
    id: 2,
    title: "Emotional Weather Widget",
    description: "A dynamic visual widget representing a fictional 'emotional weather' status. Features smooth animations with Framer Motion to reflect changing states.",
    technologies: ["React", "TailwindCSS", "Framer Motion", "SVG"],
    link: "#weather-widget-demo" // Placeholder link
  },
  {
    id: 3,
    title: "LoFi Music Player",
    description: "A minimalist music player designed for background LoFi tracks. Focuses on simple audio controls and a calming, unobtrusive user interface.",
    technologies: ["React", "HTML5 Audio", "TailwindCSS", "UI Design"],
    link: "#music-player-demo" // Placeholder link
  },
  {
    id: 4,
    title: "AI-Powered Code Assistant",
    description: "An exploration into integrating AI for code suggestions and generation directly within a custom development environment. (Conceptual)",
    technologies: ["Python", "OpenAI API", "React", "API Integration"],
    // link: "#" // No link as it's conceptual
  },
  {
    id: 5,
    title: "Portfolio Website v1",
    description: "The very playground you are exploring! Built to showcase various experiments and UI components using modern web technologies.",
    technologies: ["React", "React Router", "TailwindCSS", "Framer Motion", "Vite"],
    link: "/"
  },
  {
    id: 6,
    title: "Data Visualization Dashboard",
    description: "A concept for a dashboard that presents complex datasets in an intuitive and interactive manner using charting libraries and dynamic filtering.",
    technologies: ["React", "D3.js (or similar)", "API fetching", "Data Processing"],
    // link: "#" // Placeholder
  }
];

const Projects = () => {
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };

  const staggerContainerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15, // Time delay between each child animation
        delayChildren: 0.2,   // Delay before starting the first child animation
      },
    },
  };

  // ExperimentCard itself is already a motion.div, so we pass item variants to it.
  // The actual animation definition is within ExperimentCard.jsx (whileHover, etc.)
  // Here, we just define the variants for staggering.
   const staggerItemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    show: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: 'spring', stiffness: 100, damping: 12 }
    }
  };


  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      className="bg-slate-900 text-white p-8 min-h-screen"
    >
      <div className="container mx-auto">
        <motion.h1
          className="text-4xl font-bold mb-10 text-center text-sky-400"
          initial={{opacity:0, y: -30}}
          animate={{opacity:1, y: 0}}
          transition={{delay: 0.1, type: 'spring'}}
        >
          My Experiments & Projects
        </motion.h1>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={staggerContainerVariants}
          initial="hidden"
          animate="show"
        >
          {projectsData.map(project => (
            <ExperimentCard
              key={project.id}
              title={project.title}
              description={project.description}
              technologies={project.technologies}
              link={project.link}
              // Pass variants prop for staggered animation if ExperimentCard is a motion component
              // ExperimentCard is already a motion component, so it will pick up these variants
              variants={staggerItemVariants}
            />
          ))}
        </motion.div>

        {/* Add Prompt Playground section */}
        <motion.div className="mt-12" initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ delay: 0.5, duration: 0.5 }}>
          <PromptPlayground />
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Projects;
