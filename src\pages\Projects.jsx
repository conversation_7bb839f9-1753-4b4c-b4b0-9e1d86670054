// src/pages/Projects.jsx
// Showcases a collection of projects or experiments with enhanced visual design.

import React from 'react';
import { motion } from 'framer-motion';
import ExperimentCard from '../components/ExperimentCard';
import PromptPlayground from '../components/PromptPlayground'; // Import PromptPlayground

const projectsData = [
  {
    id: 1,
    title: "Interactive Terminal UI",
    description: "A mock terminal interface that simulates basic command execution and directory navigation. Built with React and styled with TailwindCSS for a retro-modern feel.",
    technologies: ["React", "TailwindCSS", "JavaScript", "State Management"],
    link: "#terminal-demo" // Placeholder link
  },
  {
    id: 2,
    title: "Emotional Weather Widget",
    description: "A dynamic visual widget representing a fictional 'emotional weather' status. Features smooth animations with Framer Motion to reflect changing states.",
    technologies: ["React", "TailwindCSS", "Framer Motion", "SVG"],
    link: "#weather-widget-demo" // Placeholder link
  },
  {
    id: 3,
    title: "LoFi Music Player",
    description: "A minimalist music player designed for background LoFi tracks. Focuses on simple audio controls and a calming, unobtrusive user interface.",
    technologies: ["React", "HTML5 Audio", "TailwindCSS", "UI Design"],
    link: "#music-player-demo" // Placeholder link
  },
  {
    id: 4,
    title: "AI-Powered Code Assistant",
    description: "An exploration into integrating AI for code suggestions and generation directly within a custom development environment. (Conceptual)",
    technologies: ["Python", "OpenAI API", "React", "API Integration"],
    // link: "#" // No link as it's conceptual
  },
  {
    id: 5,
    title: "Portfolio Website v1",
    description: "The very playground you are exploring! Built to showcase various experiments and UI components using modern web technologies.",
    technologies: ["React", "React Router", "TailwindCSS", "Framer Motion", "Vite"],
    link: "/"
  },
  {
    id: 6,
    title: "Data Visualization Dashboard",
    description: "A concept for a dashboard that presents complex datasets in an intuitive and interactive manner using charting libraries and dynamic filtering.",
    technologies: ["React", "D3.js (or similar)", "API fetching", "Data Processing"],
    // link: "#" // Placeholder
  }
];

const Projects = () => {
  const pageVariants = {
    initial: { opacity: 0 },
    animate: { opacity: 1, transition: { duration: 0.5 } },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };

  const staggerContainerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15, // Time delay between each child animation
        delayChildren: 0.2,   // Delay before starting the first child animation
      },
    },
  };

  // ExperimentCard itself is already a motion.div, so we pass item variants to it.
  // The actual animation definition is within ExperimentCard.jsx (whileHover, etc.)
  // Here, we just define the variants for staggering.
   const staggerItemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    show: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: 'spring', stiffness: 100, damping: 12 }
    }
  };

  // Floating particles animation
  const particleVariants = {
    animate: {
      y: [-20, -100, -20],
      x: [-10, 10, -10],
      opacity: [0, 1, 0],
      transition: {
        duration: 8,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      className="relative bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-8 min-h-screen overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Gradient orbs */}
        <motion.div
          className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"
          animate={{
            x: [0, -80, 0],
            y: [0, 30, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-sky-400/40 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            variants={particleVariants}
            animate="animate"
            transition={{
              delay: i * 1.5,
              duration: 8 + i,
              repeat: Infinity,
            }}
          />
        ))}

        {/* Grid pattern overlay */}
        <div 
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      <div className="container mx-auto relative z-10">
        {/* Enhanced header with decorative elements */}
        <div className="relative mb-16">
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial={{ scale: 0, rotate: 180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.8, type: 'spring' }}
          >
            <div className="w-32 h-32 border border-sky-400/20 rounded-full" />
          </motion.div>
          
          <motion.h1
            className="relative text-5xl md:text-6xl font-bold text-center bg-gradient-to-r from-sky-400 via-blue-500 to-purple-500 bg-clip-text text-transparent"
            initial={{opacity:0, y: -30, scale: 0.9}}
            animate={{opacity:1, y: 0, scale: 1}}
            transition={{delay: 0.1, type: 'spring', stiffness: 100}}
          >
            My Experiments & Projects
          </motion.h1>
          
          <motion.div
            className="flex justify-center mt-4"
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: "auto" }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            <div className="h-1 w-32 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full" />
          </motion.div>
        </div>

        {/* Enhanced project grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10"
          variants={staggerContainerVariants}
          initial="hidden"
          animate="show"
        >
          {projectsData.map((project, index) => (
            <motion.div
              key={project.id}
              className="relative group"
              variants={staggerItemVariants}
              whileHover={{ 
                scale: 1.02,
                transition: { type: "spring", stiffness: 400, damping: 10 }
              }}
            >
              {/* Glow effect behind cards */}
              <div className="absolute -inset-1 bg-gradient-to-r from-sky-400/20 to-purple-500/20 rounded-xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              
              <ExperimentCard
                title={project.title}
                description={project.description}
                technologies={project.technologies}
                link={project.link}
                variants={staggerItemVariants}
              />
              
              {/* Floating number indicator */}
              <motion.div
                className="absolute -top-3 -right-3 w-8 h-8 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  delay: 0.3 + index * 0.1, 
                  type: 'spring', 
                  stiffness: 200,
                  damping: 15
                }}
                whileHover={{ 
                  scale: 1.2,
                  rotate: 360,
                  transition: { duration: 0.3 }
                }}
              >
                {project.id}
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Prompt Playground section */}
        <motion.div 
          className="mt-20 relative"
          initial={{ opacity: 0, y: 50 }} 
          animate={{ opacity: 1, y: 0 }} 
          transition={{ delay: 0.8, duration: 0.6, type: 'spring' }}
        >
          {/* Section separator */}
          <motion.div
            className="flex items-center justify-center mb-12"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ delay: 0.9, duration: 0.8 }}
          >
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-sky-400/50 to-transparent" />
            <motion.div
              className="mx-6 px-4 py-2 bg-gradient-to-r from-sky-400/10 to-purple-500/10 rounded-full border border-sky-400/20"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <span className="text-sky-400 font-medium text-sm">Interactive Tools</span>
            </motion.div>
            <div className="flex-1 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent" />
          </motion.div>

          {/* Playground container with enhanced styling */}
          <motion.div
            className="relative"
            whileHover={{ y: -5 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-sky-400/5 to-purple-500/5 rounded-2xl blur-xl" />
            <div className="relative bg-slate-800/50 backdrop-blur-sm border border-sky-400/20 rounded-2xl p-1">
              <PromptPlayground />
            </div>
          </motion.div>
        </motion.div>

        {/* Footer decoration */}
        <motion.div
          className="flex justify-center mt-20 mb-8"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.2, duration: 0.5 }}
        >
          <div className="flex space-x-2">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                }}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Projects;