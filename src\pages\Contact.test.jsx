import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import Contact from './Contact'

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
    button: ({ children, ...props }) => <button {...props}>{children}</button>,
  },
}))

const ContactWrapper = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
)

describe('Contact Page', () => {
  it('renders the main heading', () => {
    render(
      <ContactWrapper>
        <Contact />
      </ContactWrapper>
    )
    
    expect(screen.getByText('Entre em Contato')).toBeInTheDocument()
  })

  it('renders contact methods', () => {
    render(
      <ContactWrapper>
        <Contact />
      </ContactWrapper>
    )
    
    expect(screen.getByText('Email')).toBeInTheDocument()
    expect(screen.getByText('LinkedIn')).toBeInTheDocument()
    expect(screen.getByText('GitHub')).toBeInTheDocument()
    expect(screen.getByText('WhatsApp')).toBeInTheDocument()
  })

  it('renders the contact form', () => {
    render(
      <ContactWrapper>
        <Contact />
      </ContactWrapper>
    )
    
    expect(screen.getByText('Envie uma Mensagem')).toBeInTheDocument()
    expect(screen.getByLabelText('Nome')).toBeInTheDocument()
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Assunto')).toBeInTheDocument()
    expect(screen.getByLabelText('Mensagem')).toBeInTheDocument()
  })

  it('handles form submission', async () => {
    render(
      <ContactWrapper>
        <Contact />
      </ContactWrapper>
    )
    
    const nameInput = screen.getByLabelText('Nome')
    const emailInput = screen.getByLabelText('Email')
    const subjectInput = screen.getByLabelText('Assunto')
    const messageInput = screen.getByLabelText('Mensagem')
    const submitButton = screen.getByText('Enviar Mensagem')
    
    fireEvent.change(nameInput, { target: { value: 'João Silva' } })
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(subjectInput, { target: { value: 'Teste' } })
    fireEvent.change(messageInput, { target: { value: 'Mensagem de teste' } })
    
    fireEvent.click(submitButton)
    
    expect(screen.getByText('Enviando...')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('✅ Mensagem enviada com sucesso!')).toBeInTheDocument()
    }, { timeout: 2000 })
  })

  it('displays contact information', () => {
    render(
      <ContactWrapper>
        <Contact />
      </ContactWrapper>
    )
    
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('linkedin.com/in/alexandrepohl')).toBeInTheDocument()
    expect(screen.getByText('github.com/alexandrepohl')).toBeInTheDocument()
    expect(screen.getByText('+55 (11) 99999-9999')).toBeInTheDocument()
  })
})
