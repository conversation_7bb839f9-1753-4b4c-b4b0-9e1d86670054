import React from 'react';
import { motion } from 'framer-motion';
import ForceGraph from '../components/ForceGraph';
import graphData from '../../data/graphData.json'; // Adjust path as needed

const GraphPage = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container mx-auto p-4"
    >
      <h1 className="text-2xl font-bold mb-4">Graph Visualization</h1>
      <ForceGraph data={graphData} />
    </motion.div>
  );
};

export default GraphPage;
