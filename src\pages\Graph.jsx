import React from 'react';
import { motion } from 'framer-motion';
import ForceGraph from '../components/ForceGraph';
import graphData from '../../data/graphData.json'; // Adjust path as needed

const GraphPage = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-black text-white p-8"
    >
      <div className="container mx-auto">
        <motion.h1
          className="text-4xl font-bold mb-8 text-center text-white"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.6 }}
        >
          Graph Visualization
        </motion.h1>

        <motion.div
          className="bg-gray-900 border border-gray-700 rounded-lg p-6 shadow-2xl"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.4, duration: 0.6 }}
        >
          <ForceGraph data={graphData} />
        </motion.div>

        <motion.div
          className="mt-8 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.6 }}
        >
          <p className="text-gray-400 text-sm">
            Interactive force-directed graph visualization using D3.js
          </p>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default GraphPage;
