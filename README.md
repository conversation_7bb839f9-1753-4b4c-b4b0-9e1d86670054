# Dev10x Playground

An interactive portfolio playground built by a developer who uses AI as a creative and technical multiplier. This project showcases a variety of UI components, animations, and a dynamic user experience, all while demonstrating modern web development practices.

## Technology Stack

-   **Frontend:** React (with Vite)
-   **Routing:** React Router DOM
-   **Styling:** TailwindCSS
-   **Animations:** Framer Motion
-   **Icons:** React Icons
-   **Version Control:** Git & GitHub
-   **Deployment:** GitHub Pages

## Local Development

Instructions to get the project running locally:

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/dev10x-playground.git # Replace your-username
    cd dev10x-playground
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Run the development server:**
    ```bash
    npm run dev
    ```
    This will start the Vite development server, typically at `http://localhost:5173`.

## GitHub Pages Deployment

This project is configured for easy deployment to GitHub Pages.

1.  **Configure `vite.config.js`**:
    *   The `vite.config.js` file has already been configured with the necessary `base` path for GitHub Pages. It is set to `base: '/dev10x-playground/'`. If your repository name is different, you'll need to update this path accordingly (e.g., `base: '/your-repo-name/'`).

2.  **Build the project:**
    ```bash
    npm run build
    ```
    This command bundles the application and outputs the static assets to the `/dist` folder.

3.  **Deploy to GitHub Pages:**
    *   Push your code to your GitHub repository, ensuring the `/dist` folder is committed if you are not using a CI/CD workflow.
    *   Go to your repository settings on GitHub.
    *   Navigate to the "Pages" section (under "Code and automation" in the sidebar).
    *   Under "Build and deployment", for "Source", select "Deploy from a branch".
    *   Choose the branch you want to deploy from (e.g., `main` or `master`).
    *   For the folder, select `/dist`.
    *   Click "Save". GitHub Pages will then build and deploy your site. It might take a few minutes for the site to become live.

## Project Structure

The project is organized to maintain clarity and scalability:

```
/public                 # Static assets (index.html, favicons, audio, etc.)
/src                    # Main source code
    /assets             # Images, fonts (if any)
    /components         # Reusable React components
        Header.jsx
        Footer.jsx
        Terminal.jsx
        WeatherWidget.jsx
        MusicPlayer.jsx
        ExperimentCard.jsx
    /pages              # Page-level components (routed views)
        Home.jsx
        About.jsx
        Projects.jsx
    /styles             # Global styles (now primarily in index.css)
        tailwind.css      # Tailwind's generated CSS (often imported in index.css or main.jsx)
    App.jsx             # Main application component (layout, routing)
    main.jsx            # React application entry point
    index.css           # Global styles, Tailwind imports, font definitions
/docs                   # Project documentation (if any beyond README)
README.md               # This file
tailwind.config.js      # TailwindCSS configuration
vite.config.js          # Vite configuration
package.json            # Project dependencies and scripts
```

### Component Overview:

-   **`Header.jsx`**: Site navigation and branding. Sticky, responsive.
-   **`Footer.jsx`**: Copyright, social links.
-   **`Terminal.jsx`**: A mock terminal with interactive commands and command history.
-   **`WeatherWidget.jsx`**: A visual representation of a creative/emotional "weather" status, with dynamic backgrounds and animations.
-   **`MusicPlayer.jsx`**: Background LoFi music player with playback, volume, and progress controls.
-   **`ExperimentCard.jsx`**: Animated cards to showcase individual projects or interactive elements.
-   **`Home.jsx` / `About.jsx` / `Projects.jsx`**: Main pages of the application, each with distinct content and layouts.
-   **`App.jsx`**: Core application wrapper, handles global layout (flex column, sticky header/footer) and routing.
-   **`main.jsx`**: Initializes the React application and imports `index.css`.

## Features & Roadmap

### Implemented (Core Functionality)

-   **`Terminal` Component**: Basic commands (`help`, `whoami`, `projects`, `date`, `clear`, `contact`, `skills`) and output, command history.
-   **`WeatherWidget` Component**: Visual states, dynamic text/icons/colors, smooth transitions.
-   **`MusicPlayer` Component**: Audio playback, volume, progress controls for a sample track.
-   **`ExperimentCard` Component**: Reusable card for displaying projects with details and animations.
-   **Pages**: `Home`, `About`, and `Projects` pages populated with components and content.
-   **Routing**: Client-side routing implemented with React Router DOM.
-   **Styling & Animations**: Full application styling with TailwindCSS and engaging animations with Framer Motion. Responsive design across components.
-   **GitHub Pages Deployment**: Project configured for deployment.
-   **Global Style Refinements**: Consistent dark theme, custom scrollbars, responsive header.

### Future Enhancements

-   **Terminal**:
    *   Typing animation for responses.
    *   `open portfolio` command to navigate to projects.
    *   More fun/utility commands.
-   **ExperimentCard**:
    *   Interactive 3D tilt effect on hover.
    *   "Live Preview" button for cards linking to actual small embedded experiments.
-   **WeatherWidget**:
    *   Option for random weather changes or based on time of day.
-   **MusicPlayer**:
    *   Playlist functionality and ability to load multiple tracks.
    *   Visualizer.
-   **Content**:
    *   More interactive elements within `ExperimentCard`s.
    *   Dynamic content fetching for projects or blog posts.
-   **User Customization**:
    *   Theme switcher (light/dark mode, accent colors).
    *   Terminal appearance settings.
-   **Blog/Notes Section**: For development insights or project updates.

## Customization

This playground is designed to be easily customizable for your own portfolio or experiments:

-   **Personal Information**:
    *   Update `src/pages/About.jsx` with your own philosophy, skills, and journey.
    *   Modify social media links in `src/components/Footer.jsx`.
-   **Project Showcase**:
    *   Edit the `projectsData` array in `src/pages/Projects.jsx` to feature your own projects.
-   **Weather Widget Moods**:
    *   Customize the `emotionalWeathers` array in `src/components/WeatherWidget.jsx` with different icons, colors, and descriptions.
-   **Music Track**:
    *   Replace `public/audio/lofi-sample.mp3` with your preferred audio file (ensure it's royalty-free if deploying publicly). Update the track title in `src/components/MusicPlayer.jsx` if needed.
-   **Terminal Commands**:
    *   Extend or modify the command list and responses in `src/components/Terminal.jsx`.

## Screenshots

Adding screenshots of the application in action (e.g., the Home page, Terminal interface, Projects grid) would greatly enhance this README. Consider using tools like ShareX or your OS's built-in screenshot capabilities.

---

This project structure is designed to be the foundation for a unique and interactive portfolio. Feel free to fork, customize, and make it your own!
