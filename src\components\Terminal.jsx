// src/components/Terminal.jsx
// An interactive terminal component.

import React, { useState, useEffect, useRef, useContext } from 'react'; // Added useContext, though not used in this simplified version
import { motion, AnimatePresence } from 'framer-motion'; // Added AnimatePresence
import { Link } from 'react-router-dom'; // For 'projects' command
import '../styles/terminal.css'; // Import terminal-specific styles

const Terminal = () => {
  const initialWelcomeMessage = {
    type: 'response',
    text: "Welcome to Dev10x Terminal. Type 'help' for available commands."
  };

  const [output, setOutput] = useState([initialWelcomeMessage]);
  const [input, setInput] = useState('');
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  // State for Lofi dance effect
  const [isDancing, setIsDancing] = useState(false);

  const inputRef = useRef(null);
  const outputEndRef = useRef(null);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  // Auto-scroll to bottom
  useEffect(() => {
    outputEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [output]);

  const handleInputChange = (e) => {
    setInput(e.target.value);
  };

  const processCommand = (command) => {
    let newOutput = [];
    switch (command.toLowerCase()) {
      case 'sudo dance':
        newOutput = [{ type: 'response', text: 'Grooving... Lofi style!' }];
        setIsDancing(true);
        setTimeout(() => {
          setIsDancing(false);
        }, 5000); // Dance for 5 seconds
        break;
      case 'help':
        newOutput = [
          { type: 'response', text: 'Available commands:' },
          { type: 'response', text: '  help          - Show this help message.' },
          { type: 'response', text: '  whoami        - Display developer information.' },
          { type: 'response', text: '  projects      - List featured projects.' },
          { type: 'response', text: '  date          - Show current date and time.' },
          { type: 'response', text: '  clear         - Clear the terminal output.' },
          { type: 'response', text: '  contact       - Show contact information.' },
          { type: 'response', text: '  skills        - List core skills.' },
        ];
        break;
      case 'whoami':
        newOutput = [
          { type: 'response', text: 'User: guest' },
          { type: 'response', text: 'Developer: A curious mind exploring AI-assisted development.' },
          { type: 'response', text: 'Purpose: To build, learn, and innovate.' },
        ];
        break;
      case 'projects':
        // This could be enhanced to fetch project names dynamically or show more details
        newOutput = [
          { type: 'response', text: 'Featured projects:' },
          { type: 'response', text: '  - Interactive Terminal UI (you are here!)' },
          { type: 'response', text: '  - Emotional Weather Widget' },
          { type: 'response', text: '  - LoFi Music Player' },
          { type: 'response', text: '  (See all projects on the ' },
          { type: 'jsx', jsx: <Link to="/projects" className="text-yellow-400 underline hover:text-yellow-300">Projects Page</Link> },
          { type: 'response', text: ' )'},
        ];
        break;
      case 'date':
        newOutput = [{ type: 'response', text: new Date().toLocaleString() }];
        break;
      case 'clear':
        setOutput([initialWelcomeMessage]); // Reset to welcome message
        return; // Skip adding to history or further output processing
      case 'contact':
        newOutput = [
            { type: 'response', text: 'You can find more about my work or get in touch via:'},
            { type: 'response', text: '  GitHub:   (placeholder: your-github-profile)'},
            { type: 'response', text: '  LinkedIn: (placeholder: your-linkedin-profile)'},
        ];
        break;
      case 'skills':
        newOutput = [
            { type: 'response', text: 'Core Skills:'},
            { type: 'response', text: '  - Frontend: React, Next.js, JavaScript, TypeScript'},
            { type: 'response', text: '  - Styling:  TailwindCSS, Framer Motion, SCSS'},
            { type: 'response', text: '  - Backend:  Node.js, Python (basics)'},
            { type: 'response', text: '  - Other:    Git, Docker, AI Integration'},
        ];
        break;
      default:
        newOutput = [{ type: 'response', text: `command not found: ${command}` }];
    }
    setOutput(prevOutput => [...prevOutput, ...newOutput]);
  };

  const handleCommandSubmit = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const command = input.trim();

      if (command) {
        setOutput(prevOutput => [...prevOutput, { type: 'input', text: command }]);
        processCommand(command);
        if (command !== history[history.length -1]) { // Avoid duplicate entries
            setHistory(prevHistory => [...prevHistory, command]);
        }
        setHistoryIndex(history.length); // Point to the new end of history for next up-arrow
      } else {
        // If input is empty, just add a new prompt line essentially
        setOutput(prevOutput => [...prevOutput, { type: 'input', text: '' }]);
      }
      setInput('');
      inputRef.current?.focus(); // Re-focus after command
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (history.length > 0) {
        const newIndex = historyIndex <= 0 ? history.length - 1 : historyIndex - 1;
        setHistoryIndex(newIndex);
        setInput(history[newIndex] || '');
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (history.length > 0 && historyIndex !== -1) { // only if already navigating history
        const newIndex = historyIndex >= history.length - 1 ? -1 : historyIndex + 1; // -1 means go to empty input
        setHistoryIndex(newIndex);
        setInput(history[newIndex] || '');
      }
    } else if (e.key === 'l' && e.ctrlKey) { // Ctrl+L to clear
        e.preventDefault();
        setOutput([initialWelcomeMessage]);
    }
  };

  const lineVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.2 } },
  };

  return (
    <>
      {isDancing && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
          className="fixed inset-0 bg-purple-500 bg-opacity-30 pointer-events-none z-40" // Ensure this z-index is below terminal if terminal is also fixed/absolute
          style={{ filter: 'blur(10px)' }}
        >
          {/* Visual effects */}
        </motion.div>
      )}
      <div
        className={`bg-black text-green-400 font-mono p-4 rounded-lg shadow-xl h-full flex flex-col selection:bg-green-700 selection:text-white ${isDancing ? 'terminal-glow' : ''}`}
        onClick={() => inputRef.current?.focus()} // Focus on click anywhere in terminal
      >
        <div className="overflow-y-auto flex-grow mb-2 pr-2"> {/* Added pr-2 for scrollbar spacing */}
          {output.map((line, index) => (
            <motion.div
            key={index}
            variants={lineVariants}
            initial="hidden"
            animate="visible"
          >
            {line.type === 'input' && <span className="text-blue-400">&gt; </span>}
            {line.type === 'jsx' ? line.jsx : <span className={line.type === 'response' ? 'text-green-400' : 'text-blue-400'}>{line.text}</span>}
          </motion.div>
        ))}
        <div ref={outputEndRef} /> {/* For auto-scrolling */}
        </div>
        <div className="flex items-center">
          <span className="text-blue-400">&gt;&nbsp;</span>
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={handleInputChange}
            onKeyDown={(e) => { handleCommandSubmit(e); handleKeyDown(e); }}
            className="bg-transparent border-none outline-none flex-grow text-green-400 caret-green-400"
            autoFocus
          />
        </div>
      </div>
    </>
  );
};

export default Terminal;
