{"nodes": [{"id": "React", "group": 1}, {"id": "JavaScript", "group": 1}, {"id": "TypeScript", "group": 1}, {"id": "Node.js", "group": 2}, {"id": "Express", "group": 2}, {"id": "MongoDB", "group": 3}, {"id": "PostgreSQL", "group": 3}, {"id": "TailwindCSS", "group": 4}, {"id": "Framer Motion", "group": 4}, {"id": "D3.js", "group": 5}, {"id": "Vite", "group": 6}, {"id": "Git", "group": 7}], "links": [{"source": "React", "target": "JavaScript", "value": 3}, {"source": "React", "target": "TypeScript", "value": 2}, {"source": "JavaScript", "target": "Node.js", "value": 3}, {"source": "Node.js", "target": "Express", "value": 3}, {"source": "Express", "target": "MongoDB", "value": 2}, {"source": "Express", "target": "PostgreSQL", "value": 2}, {"source": "React", "target": "TailwindCSS", "value": 2}, {"source": "React", "target": "Framer Motion", "value": 2}, {"source": "JavaScript", "target": "D3.js", "value": 2}, {"source": "React", "target": "Vite", "value": 1}, {"source": "JavaScript", "target": "Git", "value": 1}, {"source": "TypeScript", "target": "Node.js", "value": 2}, {"source": "D3.js", "target": "React", "value": 1}]}