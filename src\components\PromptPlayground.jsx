import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { prompts } from '../data/prompts'; // Adjust path as needed
import { FaCopy } from 'react-icons/fa';

const PromptPlayground = () => {
  const [copiedPrompt, setCopiedPrompt] = useState(null);

  const handleCopy = (promptText, id) => {
    navigator.clipboard.writeText(promptText);
    setCopiedPrompt(id);
    setTimeout(() => setCopiedPrompt(null), 2000); // Reset after 2 seconds
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-gray-800 p-6 rounded-lg shadow-xl"
    >
      <h2 className="text-xl font-semibold text-accent mb-4">Prompt Playground</h2>
      <div className="space-y-4">
        {prompts.map((item) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: item.id * 0.1 }}
            className="bg-gray-700 p-4 rounded-md"
          >
            <h3 className="font-medium text-white mb-2">{item.title}</h3>
            <p className="text-sm text-gray-300 mb-3 whitespace-pre-wrap">{item.prompt}</p>
            <button
              onClick={() => handleCopy(item.prompt, item.id)}
              className="flex items-center px-3 py-1 bg-accent hover:bg-accent-dark text-white text-sm rounded-md transition-colors"
            >
              <FaCopy className="mr-2" />
              {copiedPrompt === item.id ? 'Copied!' : 'Copy Prompt'}
            </button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default PromptPlayground;
