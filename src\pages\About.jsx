// src/pages/About.jsx
// Provides information about the developer, skills, and philosophy.

import React from 'react';
import { motion } from 'framer-motion';

const About = () => {
  const pageVariants = {
    initial: { opacity: 0, x: -50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 50 },
  };

  const pageTransition = {
    type: 'spring',
    stiffness: 100,
    damping: 20,
    duration: 0.5,
  };

  const sectionAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 }
  };

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
      // className="bg-slate-900 text-gray-300 p-8 min-h-screen" // Classes moved to App.jsx or index.css
    >
      <div className="max-w-3xl mx-auto"> {/* This specific max-width for content is fine */}
        <motion.h1
          className="text-4xl font-bold text-white mb-8 text-center"
          initial={{ opacity:0, y: -30}}
          animate={{ opacity:1, y: 0}}
          transition={{delay: 0.2, type: 'spring'}}
        >
          About Me & This Playground
        </motion.h1>

        {/* Section 1: Developer Philosophy */}
        <motion.section
          className="mb-10"
          variants={sectionAnimation}
          initial="initial"
          animate="animate"
          transition={{ ...sectionAnimation.transition, delay: 0.3 }}
        >
          <h2 className="text-3xl font-semibold text-sky-400 mb-4">My Philosophy</h2>
          <p className="text-lg leading-relaxed mb-3">
            I believe in the power of continuous learning and iterative development. This playground is a testament to that—a space where ideas can be rapidly prototyped, tested, and showcased. My approach centers on blending robust engineering principles with creative design, always aiming for elegant and efficient solutions.
          </p>
          <p className="text-lg leading-relaxed">
            Embracing AI, particularly tools like GitHub Copilot, has become a cornerstone of my development process. I view AI not just as an assistant, but as a creative partner that accelerates development, offers new perspectives, and helps push the boundaries of what's possible.
          </p>
        </motion.section>

        <hr className="border-slate-700 my-8" />

        {/* Section 2: Core Skills */}
        <motion.section
          className="mb-10"
          variants={sectionAnimation}
          initial="initial"
          animate="animate"
          transition={{ ...sectionAnimation.transition, delay: 0.5 }}
        >
          <h2 className="text-3xl font-semibold text-sky-400 mb-4">Core Skills</h2>
          <p className="text-lg leading-relaxed mb-3">
            My toolkit is diverse and constantly evolving. Key areas of expertise include:
          </p>
          <ul className="list-disc list-inside text-lg leading-relaxed space-y-2 mb-3">
            <li>Frontend: React, Next.js, HTML5, CSS3, JavaScript (ES6+), TypeScript</li>
            <li>Styling: TailwindCSS, SCSS/SASS, CSS Modules, Framer Motion</li>
            <li>Backend: Node.js, Express.js, Python (Flask/Django basics)</li>
            <li>Databases: MongoDB, PostgreSQL, Firebase</li>
            <li>DevOps & Tools: Git, Docker, CI/CD (GitHub Actions), VS Code</li>
            <li>AI Integration: Prompt Engineering, API usage (OpenAI, etc.), AI-assisted development</li>
          </ul>
          <p className="text-lg leading-relaxed">
            I'm passionate about building full-stack applications that are both performant and user-friendly.
          </p>
        </motion.section>

        <hr className="border-slate-700 my-8" />

        {/* Section 3: AI in Development */}
        <motion.section
          variants={sectionAnimation}
          initial="initial"
          animate="animate"
          transition={{ ...sectionAnimation.transition, delay: 0.7 }}
        >
          <h2 className="text-3xl font-semibold text-sky-400 mb-4">AI in My Development Workflow</h2>
          <p className="text-lg leading-relaxed mb-3">
            Artificial Intelligence is transforming software development, and I actively integrate AI tools to enhance productivity and creativity. My use of AI includes:
          </p>
          <ul className="list-disc list-inside text-lg leading-relaxed space-y-2">
            <li><strong>Rapid Prototyping:</strong> Using AI to quickly generate boilerplate code, component structures, and initial design ideas. This allows for faster iteration cycles.</li>
            <li><strong>Code Generation & Assistance:</strong> Leveraging tools like GitHub Copilot for intelligent code suggestions, autocompletion, and generating utility functions.</li>
            <li><strong>Learning & Problem Solving:</strong> Consulting AI models for explanations of complex concepts, debugging assistance, and exploring alternative solutions to technical challenges.</li>
            <li><strong>Content & Asset Generation:</strong> Experimenting with AI for placeholder text, image concepts, and even data generation for testing purposes.</li>
          </ul>
          <p className="text-lg leading-relaxed mt-3">
            By strategically applying AI, I aim to streamline the development process, focus more on high-level design and complex problem-solving, and ultimately build better software faster.
          </p>
        </motion.section>
      </div>
    </motion.div>
  );
};

export default About;
