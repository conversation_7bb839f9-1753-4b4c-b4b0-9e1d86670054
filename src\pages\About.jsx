// src/pages/About.jsx
// Provides information about the developer, skills, and philosophy with enhanced modern design.

import React from 'react';
import { motion } from 'framer-motion';

const About = () => {
  const pageVariants = {
    initial: { opacity: 0, x: -50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 50 },
  };

  const pageTransition = {
    type: 'spring',
    stiffness: 100,
    damping: 20,
    duration: 0.5,
  };

  const sectionAnimation = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 }
  };

  // Card animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: { type: 'spring', stiffness: 100, damping: 15 }
    },
    hover: { 
      y: -5,
      transition: { type: 'spring', stiffness: 400, damping: 10 }
    }
  };

  // Skills data for better organization
  const skillCategories = [
    {
      title: "Frontend",
      icon: "🎨",
      skills: ["React", "Next.js", "HTML5", "CSS3", "JavaScript (ES6+)", "TypeScript"],
      color: "from-blue-400 to-cyan-500"
    },
    {
      title: "Styling",
      icon: "✨",
      skills: ["TailwindCSS", "SCSS/SASS", "CSS Modules", "Framer Motion"],
      color: "from-purple-400 to-pink-500"
    },
    {
      title: "Backend",
      icon: "⚙️",
      skills: ["Node.js", "Express.js", "Python (Flask/Django basics)"],
      color: "from-emerald-400 to-teal-500"
    },
    {
      title: "Database & Tools",
      icon: "🛠️",
      skills: ["MongoDB", "PostgreSQL", "Firebase", "Git", "Docker", "CI/CD"],
      color: "from-orange-400 to-red-500"
    },
    {
      title: "AI Integration",
      icon: "🤖",
      skills: ["Prompt Engineering", "API usage (OpenAI, etc.)", "AI-assisted development"],
      color: "from-indigo-400 to-purple-500"
    }
  ];

  return (
    <motion.div
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
      className="relative min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-gray-300 p-8 overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large gradient orbs */}
        <motion.div
          className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-full blur-3xl"
          animate={{
            x: [0, 100, 0],
            y: [0, -50, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-10 w-80 h-80 bg-gradient-to-r from-emerald-500/15 to-cyan-500/15 rounded-full blur-3xl"
          animate={{
            x: [0, -60, 0],
            y: [0, 40, 0],
            scale: [1, 0.9, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 3
          }}
        />

        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-sky-400/30 rounded-full"
            style={{
              left: `${20 + (i * 15)}%`,
              top: `${15 + (i * 12)}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              opacity: [0.3, 0.7, 0.3],
            }}
            transition={{
              duration: 8 + i,
              repeat: Infinity,
              ease: "easeInOut",
              delay: i * 0.8,
            }}
          />
        ))}

        {/* Grid overlay */}
        <div 
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}
        />
      </div>

      <div className="max-w-4xl mx-auto relative z-10">
        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <motion.div
            className="inline-block relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, type: 'spring' }}
          >
            <motion.h1
              className="text-4xl md:text-5xl font-bold text-white mb-4 bg-gradient-to-r from-sky-400 via-blue-500 to-purple-500 bg-clip-text text-transparent"
              initial={{opacity:0, y: -30}}
              animate={{opacity:1, y: 0}}
              transition={{delay: 0.2, type: 'spring'}}
            >
              About Me & This Playground
            </motion.h1>
            
            <motion.div
              className="flex justify-center mt-4"
              initial={{ opacity: 0, width: 0 }}
              animate={{ opacity: 1, width: "auto" }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              <div className="h-1 w-32 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full" />
            </motion.div>
          </motion.div>
        </div>

        {/* Section 1: Developer Philosophy */}
        <motion.section
          className="mb-12"
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          transition={{ delay: 0.3 }}
        >
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-sky-400/20 to-blue-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            
            <div className="relative bg-slate-800/60 backdrop-blur-sm border border-sky-400/20 rounded-2xl p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-sky-400 to-blue-500 rounded-full flex items-center justify-center text-2xl mr-4">
                  💭
                </div>
                <h2 className="text-3xl font-semibold bg-gradient-to-r from-sky-400 to-blue-500 bg-clip-text text-transparent">
                  My Philosophy
                </h2>
              </div>
              
              <div className="space-y-4 text-lg leading-relaxed">
                <p>
                  I believe in the power of <span className="text-sky-400 font-semibold">continuous learning</span> and <span className="text-blue-400 font-semibold">iterative development</span>. This playground is a testament to that—a space where ideas can be rapidly prototyped, tested, and showcased. My approach centers on blending robust engineering principles with creative design, always aiming for elegant and efficient solutions.
                </p>
                <p>
                  Embracing <span className="text-purple-400 font-semibold">AI</span>, particularly tools like GitHub Copilot, has become a cornerstone of my development process. I view AI not just as an assistant, but as a <span className="text-pink-400 font-semibold">creative partner</span> that accelerates development, offers new perspectives, and helps push the boundaries of what's possible.
                </p>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Section 2: Core Skills - Enhanced Grid */}
        <motion.section
          className="mb-12"
          variants={sectionAnimation}
          initial="initial"
          animate="animate"
          transition={{ delay: 0.5 }}
        >
          <div className="text-center mb-10">
            <h2 className="text-3xl md:text-4xl font-semibold bg-gradient-to-r from-emerald-400 to-teal-500 bg-clip-text text-transparent mb-4">
              Core Skills
            </h2>
            <p className="text-lg leading-relaxed max-w-2xl mx-auto">
              My toolkit is diverse and constantly evolving. Here are the key areas of expertise:
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {skillCategories.map((category, index) => (
              <motion.div
                key={category.title}
                className="relative group"
                variants={cardVariants}
                initial="hidden"
                animate="visible"
                whileHover="hover"
                transition={{ delay: 0.6 + index * 0.1 }}
              >
                <div className={`absolute -inset-1 bg-gradient-to-r ${category.color} opacity-20 rounded-xl blur group-hover:opacity-30 transition-opacity duration-300`} />
                
                <div className="relative bg-slate-800/60 backdrop-blur-sm border border-gray-600/30 rounded-xl p-6 h-full">
                  <div className="flex items-center mb-4">
                    <span className="text-2xl mr-3">{category.icon}</span>
                    <h3 className={`text-xl font-semibold bg-gradient-to-r ${category.color} bg-clip-text text-transparent`}>
                      {category.title}
                    </h3>
                  </div>
                  
                  <ul className="space-y-2">
                    {category.skills.map((skill, skillIndex) => (
                      <motion.li
                        key={skill}
                        className="text-gray-300 flex items-center"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.8 + index * 0.1 + skillIndex * 0.05 }}
                      >
                        <div className="w-2 h-2 bg-gradient-to-r from-sky-400 to-purple-500 rounded-full mr-3 flex-shrink-0" />
                        {skill}
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>

          <motion.p 
            className="text-lg leading-relaxed text-center mt-8 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
          >
            I'm passionate about building <span className="text-emerald-400 font-semibold">full-stack applications</span> that are both performant and user-friendly.
          </motion.p>
        </motion.section>

        {/* Section 3: AI in Development */}
        <motion.section
          variants={cardVariants}
          initial="hidden"
          animate="visible"
          whileHover="hover"
          transition={{ delay: 0.7 }}
        >
          <div className="relative group">
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-400/20 to-pink-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
            
            <div className="relative bg-slate-800/60 backdrop-blur-sm border border-purple-400/20 rounded-2xl p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full flex items-center justify-center text-2xl mr-4">
                  🤖
                </div>
                <h2 className="text-3xl font-semibold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
                  AI in My Development Workflow
                </h2>
              </div>
              
              <div className="space-y-6">
                <p className="text-lg leading-relaxed">
                  Artificial Intelligence is transforming software development, and I actively integrate AI tools to enhance productivity and creativity. My use of AI includes:
                </p>
                
                <div className="grid gap-4">
                  {[
                    {
                      title: "Rapid Prototyping",
                      description: "Using AI to quickly generate boilerplate code, component structures, and initial design ideas. This allows for faster iteration cycles.",
                      icon: "⚡"
                    },
                    {
                      title: "Code Generation & Assistance",
                      description: "Leveraging tools like GitHub Copilot for intelligent code suggestions, autocompletion, and generating utility functions.",
                      icon: "💻"
                    },
                    {
                      title: "Learning & Problem Solving",
                      description: "Consulting AI models for explanations of complex concepts, debugging assistance, and exploring alternative solutions to technical challenges.",
                      icon: "🧠"
                    },
                    {
                      title: "Content & Asset Generation",
                      description: "Experimenting with AI for placeholder text, image concepts, and even data generation for testing purposes.",
                      icon: "🎯"
                    }
                  ].map((item, index) => (
                    <motion.div
                      key={item.title}
                      className="flex items-start space-x-4 p-4 bg-slate-700/30 rounded-lg border border-purple-400/10"
                      initial={{ opacity: 0, x: -30 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.9 + index * 0.1 }}
                    >
                      <span className="text-2xl flex-shrink-0">{item.icon}</span>
                      <div>
                        <h4 className="font-semibold text-purple-400 mb-1">{item.title}:</h4>
                        <p className="text-gray-300">{item.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
                
                <motion.p 
                  className="text-lg leading-relaxed border-l-4 border-purple-400 pl-4 bg-purple-400/5 rounded-r-lg p-4"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.4 }}
                >
                  By strategically applying AI, I aim to streamline the development process, focus more on high-level design and complex problem-solving, and ultimately <span className="text-purple-400 font-semibold">build better software faster</span>.
                </motion.p>
              </div>
            </div>
          </div>
        </motion.section>

        {/* Bottom decorative elements */}
        <motion.div
          className="flex justify-center mt-16 mb-8"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.6, duration: 0.5 }}
        >
          <div className="flex space-x-2">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="w-3 h-3 bg-gradient-to-r from-sky-400 via-purple-500 to-pink-500 rounded-full"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.3,
                }}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default About;